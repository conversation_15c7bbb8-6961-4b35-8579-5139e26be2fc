#!/usr/bin/env python3
"""
Connector gRPC Server

This server handles all connector-related operations including:
- Adding/removing sources
- Syncing data from sources
- Managing connector configurations
- File operations (list, sync, etc.)
"""

import os
import sys
import grpc
import structlog
from concurrent import futures
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.grpc_ import connector_pb2_grpc
from app.services.connector_wrapper_service import ConnectorWrapperService

# Load environment variables
load_dotenv()

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

def serve():
    """Start the Connector gRPC server"""
    # Get server configuration from environment
    port = os.getenv("CONNECTOR_GRPC_PORT", "50052")
    max_workers = int(os.getenv("CONNECTOR_MAX_WORKERS", "10"))
    
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=max_workers))
    
    # Add the connector service
    connector_service = ConnectorWrapperService()
    connector_pb2_grpc.add_ConnectorServiceServicer_to_server(connector_service, server)
    
    # Configure server address
    listen_addr = f'[::]:{port}'
    server.add_insecure_port(listen_addr)
    
    # Start server
    server.start()
    logger.info(f"Connector gRPC server started on port {port}")
    logger.info(f"Server listening on {listen_addr}")
    logger.info(f"Max workers: {max_workers}")
    
    try:
        server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("Shutting down Connector gRPC server...")
        server.stop(0)

if __name__ == '__main__':
    serve()