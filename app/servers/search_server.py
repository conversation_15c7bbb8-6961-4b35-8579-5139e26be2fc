#!/usr/bin/env python3
"""
Search gRPC Server

This server handles all search-related operations including:
- Semantic document search
- Batch search operations
- Advanced search with filters
- Search suggestions and autocomplete
- Search analytics
"""

import os
import sys
import grpc
import structlog
from concurrent import futures
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.grpc_ import search_pb2_grpc
from app.services.search_wrapper_service import SearchWrapperService

# Load environment variables
load_dotenv()

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

def serve():
    """Start the Search gRPC server"""
    # Get server configuration from environment
    port = os.getenv("SEARCH_GRPC_PORT", "50053")
    max_workers = int(os.getenv("SEARCH_MAX_WORKERS", "10"))
    
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=max_workers))
    
    # Add the search service
    search_service = SearchWrapperService()
    search_pb2_grpc.add_SearchServiceServicer_to_server(search_service, server)
    
    # Configure server address
    listen_addr = f'[::]:{port}'
    server.add_insecure_port(listen_addr)
    
    # Start server
    server.start()
    logger.info(f"Search gRPC server started on port {port}")
    logger.info(f"Server listening on {listen_addr}")
    logger.info(f"Max workers: {max_workers}")
    
    try:
        server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("Shutting down Search gRPC server...")
        server.stop(0)

if __name__ == '__main__':
    serve()