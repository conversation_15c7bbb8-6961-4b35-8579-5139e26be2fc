from concurrent.futures import Thread<PERSON>oolExecutor

from celery import shared_task

from app.modules.connectors.handlers.gdrive.services.google_drive_service import (
    GoogleDriveService,
)


@shared_task
def sync_folders_by_ids(folder_ids):
    with Thread<PERSON>oolExecutor(max_workers=4) as executor:
        # sync_folder_by_id is not defined; placeholder logic
        results = [f"Simulated sync for folder {fid}" for fid in folder_ids]
    return results


@shared_task
def sync_drive_task(organisation_id, full_sync=False, task_id=None):
    from app.modules.connectors.handlers.gdrive.services.google_drive_service import (
        GoogleDriveService,
    )

    service = GoogleDriveService()
    return service.sync_drive(organisation_id, full_sync, task_id)


@shared_task
def sync_file_by_id_task(
    file_id, agent_id, user_id, organisation_id, url=None, task_id=None
):
    service = GoogleDriveService()
    return service.sync_file_by_id(
        file_id, agent_id, user_id, organisation_id, url, task_id
    )


@shared_task
def sync_folder_recursively_task(organisation_id, folder_id, task_id=None):
    service = GoogleDriveService()
    drive = service.get_service_account_drive_service(organisation_id)
    return service.sync_folder_recursively_with_permissions(
        drive, organisation_id, folder_id, task_id
    )
