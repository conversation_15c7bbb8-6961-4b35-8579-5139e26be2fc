from typing import Any, Dict

import grpc
import structlog
from googleapiclient.discovery import build

from app.grpc_ import connector_pb2, connector_pb2_grpc, search_pb2, search_pb2_grpc
from app.modules.connectors.handlers.gdrive.services.google_drive_service import (
    GoogleDriveService,
)
from app.modules.connectors.handlers.gdrive.workers.sync_worker import (
    GoogleDriveSyncWorker,
)
from app.modules.organisation.services.organisation import OrganisationService

logger = structlog.get_logger()


def convert_properties_to_string_map(properties):
    """
    Convert a properties dictionary to a string-only map suitable for protobuf.

    Args:
        properties (dict): Dictionary with mixed value types

    Returns:
        dict: Dictionary with all values converted to strings
    """
    if not properties or not isinstance(properties, dict):
        return {}

    string_properties = {}
    for key, value in properties.items():
        # Convert key to string if needed
        str_key = str(key) if not isinstance(key, str) else key

        # Convert value to string based on type
        if isinstance(value, bool):
            str_value = "true" if value else "false"
        elif value is None:
            str_value = ""
        elif isinstance(value, (int, float)):
            str_value = str(value)
        elif isinstance(value, str):
            str_value = value
        else:
            # For complex objects, convert to string representation
            str_value = str(value)

        string_properties[str_key] = str_value

    return string_properties


class GoogleDriveGrpcService(connector_pb2_grpc.ConnectorServiceServicer):
    """
    gRPC service for Google Drive integration.
    """

    def __init__(self):
        self.drive_service = GoogleDriveService()
        self.sync_worker = GoogleDriveSyncWorker()
        self.organisation_service = OrganisationService()

    def syncDrive(self, request, context):
        """
        Schedule Google Drive sync to run in background and return immediately.
        """
        logger.info(
            "Received request to sync Google Drive",
            organisation_id=request.organisation_id,
            full_sync=request.full_sync,
        )

        try:
            # Validate organisation_id is provided
            if not request.organisation_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("organisation_id is required")
                return connector_pb2.SyncDriveResponse(
                    success=False,
                    message="organisation_id is required",
                    sync_status="failed",
                )

            # Schedule sync job in background instead of running synchronously
            job_id = self.drive_service._schedule_sync(
                user_id=request.organisation_id,  # Using organisation_id as user_id for scheduling
                organisation_id=request.organisation_id,
                full_sync=request.full_sync,
                delay_seconds=0,  # Start immediately
            )

            logger.info(f"Google Drive sync job scheduled with ID: {job_id}")

            return connector_pb2.SyncDriveResponse(
                success=True,
                message=f"Google Drive sync job scheduled successfully. Job ID: {job_id}",
                files_synced=0,  # Will be updated when job completes
                folders_synced=0,  # Will be updated when job completes
                sync_status="scheduled",
            )

        except ValueError as e:
            logger.error("Invalid parameters for Google Drive sync", error=str(e))
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details(str(e))
            return connector_pb2.SyncDriveResponse(
                success=False, message=str(e), sync_status="failed"
            )
        except Exception as e:
            logger.error("Error scheduling Google Drive sync", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error scheduling Google Drive sync: {str(e)}")
            return connector_pb2.SyncDriveResponse(success=False, sync_status="failed")

    def getSyncStatus(self, request, context):
        """
        Get the status of a sync job by job ID.
        """
        logger.info("Received request to get sync status", job_id=request.job_id)

        try:
            # Check if job exists in Redis
            job_data_str = self.drive_service.redis_service.get(request.job_id)

            if not job_data_str:
                return connector_pb2.SyncStatusResponse(
                    success=False,
                    message="Job not found or expired",
                    status="not_found",
                )

            import json

            job_data = json.loads(job_data_str)

            # Check if job is still in queue (pending)
            queue_score = self.drive_service.redis_service.zscore(
                "gdrive_sync_queue", request.job_id
            )

            if queue_score is not None:
                return connector_pb2.SyncStatusResponse(
                    success=True,
                    message="Sync job is pending in queue",
                    status="pending",
                    organisation_id=job_data.get("organisation_id", ""),
                    full_sync=job_data.get("full_sync", False),
                )
            else:
                # Job has been processed (completed or failed)
                # In a production system, you'd want to store completion status
                return connector_pb2.SyncStatusResponse(
                    success=True,
                    message="Sync job has been processed",
                    status="completed",
                    organisation_id=job_data.get("organisation_id", ""),
                    full_sync=job_data.get("full_sync", False),
                )

        except Exception as e:
            logger.error("Error getting sync status", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting sync status: {str(e)}")
            return connector_pb2.SyncStatusResponse(
                success=False, message=f"Error: {str(e)}", status="error"
            )

    def listTopLevelFolders(self, request, context):
        """
        List top-level folders from Google Drive using service account.
        """
        logger.info(
            "Received request to list top-level folders",
            organisation_id=request.organisation_id,
        )

        try:
            success, message, folders = self.drive_service.fetch_top_level_folders(
                request.organisation_id
            )

            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return connector_pb2.ListTopLevelFoldersResponse(success=False)

            # Convert folders to proto models
            folder_models = []
            for folder in folders:
                folder_model = connector_pb2.FolderInfo(
                    id=folder["id"], name=folder["name"]
                )
                folder_models.append(folder_model)

            return connector_pb2.ListTopLevelFoldersResponse(
                success=True, message=message, folders=folder_models
            )

        except Exception as e:
            logger.error("Error listing top-level folders", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing folders: {str(e)}")
            return connector_pb2.ListTopLevelFoldersResponse(success=False)

    def disconnectDrive(self, request, context):
        """
        Disconnect Google Drive for an organization (remove synced data).
        """
        logger.info(
            "Received request to disconnect Google Drive",
            organisation_id=request.organisation_id,
        )

        try:
            success, message = self.drive_service.disconnect_drive(
                request.organisation_id
            )

            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return connector_pb2.DisconnectDriveResponse(success=False)

            return connector_pb2.DisconnectDriveResponse(success=True, message=message)

        except Exception as e:
            logger.error("Error disconnecting Google Drive", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error disconnecting Google Drive: {str(e)}")
            return connector_pb2.DisconnectDriveResponse(success=False)

    def listFiles(self, request, context):
        """
        List Google Drive files and folders.
        """
        logger.info(
            "Received request to list Google Drive files",
            user_id=request.user_id,
            folder_id=request.folder_id,
        )

        try:
            files, total_count, page, page_size = self.drive_service.list_files(
                request.user_id,
                request.folder_id if request.folder_id else None,
                request.page if request.page > 0 else 1,
                request.page_size if request.page_size > 0 else 50,
            )

            # Convert to proto models
            file_models = []
            for file_data in files:
                file_model = connector_pb2.DriveFileModel(
                    id=file_data["id"],
                    name=file_data["name"],
                    mime_type=file_data.get("mime_type", ""),
                    web_view_link=file_data.get("web_view_link", ""),
                    created_time=file_data["created_time"],
                    modified_time=file_data["modified_time"],
                    size=int(file_data.get("size", 0)),
                    shared_with=file_data.get("shared_with", []),
                    is_folder=file_data["is_folder"],
                    parent_folder_id=file_data.get("parent_folder_id", ""),
                    child_count=file_data.get("child_count", 0),
                )
                file_models.append(file_model)

            return connector_pb2.ListFilesResponse(
                success=True,
                message="Files retrieved successfully",
                files=file_models,
                total_count=total_count,
                page=page,
                page_size=page_size,
            )

        except Exception as e:
            logger.error("Error listing Google Drive files", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error listing files: {str(e)}")
            return connector_pb2.ListFilesResponse(success=False)

    def getFileDetails(self, request, context):
        """
        Get file details.
        """
        logger.info(
            "Received request to get Google Drive file details",
            user_id=request.user_id,
            file_id=request.file_id,
        )

        try:
            success, message, file_data = self.drive_service.get_file_details(
                request.user_id, request.file_id
            )

            if not success or not file_data:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return connector_pb2.GetFileDetailsResponse(success=False)

            # Convert to proto model
            file_model = connector_pb2.DriveFileModel(
                id=file_data["id"],
                name=file_data["name"],
                mime_type=file_data.get("mime_type", ""),
                web_view_link=file_data.get("web_view_link", ""),
                created_time=file_data["created_time"],
                modified_time=file_data["modified_time"],
                size=int(file_data.get("size", 0)),
                shared_with=file_data.get("shared_with", []),
                is_folder=file_data["is_folder"],
                child_count=file_data.get("child_count", 0),
            )

            return connector_pb2.GetFileDetailsResponse(
                success=True, message=message, file=file_model
            )

        except Exception as e:
            logger.error("Error getting Google Drive file details", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting file details: {str(e)}")
            return connector_pb2.GetFileDetailsResponse(success=False)

    def getFolderById(self, request, context):
        """
        Get folder by ID and its contents using service account.
        """
        logger.info(
            "Received request to get Google Drive folder by ID",
            organisation_id=request.organisation_id,
            folder_id=request.folder_id,
        )

        try:
            # Get folder and contents using service account
            success, message, folder_data = (
                self.drive_service.get_folder_and_contents_by_id_service_account(
                    request.organisation_id, request.folder_id
                )
            )

            if not success or not folder_data:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(message)
                return connector_pb2.GetFolderByIdResponse(success=False)

            # Convert folder to proto model
            folder_model = connector_pb2.DriveFileModel(
                id=folder_data["id"],
                name=folder_data["name"],
                is_folder=True,
                child_count=folder_data["child_count"],
            )

            # Convert children to proto models
            children_models = []
            for child_data in folder_data.get("children", []):
                child_model = connector_pb2.DriveFileModel(
                    id=child_data["id"],
                    name=child_data["name"],
                    mime_type=child_data.get("mime_type", ""),
                    web_view_link=child_data.get("web_view_link", ""),
                    created_time=child_data["created_time"],
                    modified_time=child_data["modified_time"],
                    size=int(child_data.get("size", 0)),
                    shared_with=child_data.get("shared_with", []),
                    is_folder=child_data["is_folder"],
                    parent_folder_id=child_data.get("parent_folder_id", ""),
                    child_count=child_data.get("child_count", 0),
                )
                children_models.append(child_model)

            return connector_pb2.GetFolderByIdResponse(
                success=True,
                message=message,
                folder=folder_model,
                children=children_models,
            )

        except Exception as e:
            logger.error("Error getting Google Drive folder by ID", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting folder by ID: {str(e)}")
            return connector_pb2.GetFolderByIdResponse(success=False)

    def syncFolderByIds(self, request, context):
        """
        Sync specific folders by IDs recursively using service account via worker.
        Creates a job for the worker to process asynchronously.
        """
        logger.info(
            "Received request to sync Google Drive folders by IDs",
            organisation_id=request.organisation_id,
            folder_ids=request.folder_ids,
        )

        try:
            # Validate that we have service account credentials before scheduling job
            service = self.drive_service.get_service_account_drive_service(
                request.organisation_id
            )
            if not service:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("No Google Drive service account credentials found")
                return connector_pb2.SyncFolderByIdsResponse(
                    success=False,
                    message="No Google Drive service account credentials found",
                    sync_status="failed",
                )

            # Import worker here to avoid circular imports
            from app.modules.connectors.handlers.gdrive.workers.sync_worker import (
                GoogleDriveSyncWorker,
            )

            # Create worker instance and schedule the job
            worker = GoogleDriveSyncWorker()
            job_id = worker.schedule_folder_sync_job(
                organisation_id=request.organisation_id,
                folder_ids=list(request.folder_ids),
                delay_seconds=0,  # Process immediately
            )

            logger.info(
                f"Scheduled folder sync job {job_id} for organization {request.organisation_id}"
            )

            # Return immediate response indicating job was scheduled
            return connector_pb2.SyncFolderByIdsResponse(
                success=True,
                message=f"Folder sync job scheduled successfully. Job ID: {job_id}",
                files_synced=0,  # Will be updated when job completes
                folders_synced=0,  # Will be updated when job completes
                sync_status="scheduled",
                synced_folders=[],  # Will be populated when job completes
            )

        except Exception as e:
            logger.error("Error scheduling Google Drive folder sync job", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error scheduling folder sync job: {str(e)}")
            return connector_pb2.SyncFolderByIdsResponse(
                success=False,
                message=f"Error scheduling folder sync job: {str(e)}",
                sync_status="failed",
            )
            return connector_pb2.SyncFolderByIdsResponse(
                success=False, message=f"Error: {str(e)}", sync_status="failed"
            )

    def checkFileAccess(self, request, context):
        """
        Check if a user has access to a file or folder.
        This method recursively checks parent folders if direct access is not found.
        """
        logger.info(
            "Received request to check file access",
            user_id=request.user_id,
            file_id=request.file_id,
        )

        try:
            has_access = self.drive_service.check_file_access(
                request.user_id, request.file_id
            )

            return connector_pb2.CheckFileAccessResponse(
                success=True,
                message="Access check completed successfully",
                has_access=has_access,
            )

        except Exception as e:
            logger.error("Error checking file access", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error checking file access: {str(e)}")
            return connector_pb2.CheckFileAccessResponse(
                success=False, message=f"Error: {str(e)}", has_access=False
            )

    def searchSimilarDocuments(self, request, context):
        """
        Search for documents semantically similar to the query text.
        """
        logger.info(
            "Received request to search similar documents",
            user_id=request.user_id,
            query=request.query_text,
            agent_id=request.agent_id if hasattr(request, "agent_id") else None,
            org_id=(
                request.organisation_id if hasattr(request, "organisation_id") else None
            ),
        )

        try:
            # Extract file_ids from request if provided
            file_ids = None
            if hasattr(request, "file_ids") and request.file_ids:
                file_ids = list(request.file_ids)
                logger.info(f"Filtering search to {len(file_ids)} specific files")

            # Extract least_score from request if provided
            least_score = None
            if hasattr(request, "least_score") and request.least_score > 0:
                least_score = request.least_score
                logger.info(f"Filtering results with minimum score: {least_score}")

            success, message, results = self.drive_service.search_similar_documents(
                request.user_id,
                request.query_text,
                request.top_k if request.top_k > 0 else 5,
                (
                    request.agent_id
                    if hasattr(request, "agent_id") and request.agent_id
                    else None
                ),
                (
                    request.organisation_id
                    if hasattr(request, "organisation_id") and request.organisation_id
                    else None
                ),
                file_ids,
                least_score,
            )
            # print('search similar documents', success, message, results)
            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return search_pb2.SearchSimilarDocumentsResponse(success=False)

            # Convert to proto models (without entities/relationships in individual results)
            result_models = []

            # Check if results is a HybridResponse object with graph_context
            graph_context_data = None
            if hasattr(results, "graph_context"):
                # Results is a HybridResponse object
                graph_context_data = getattr(results, "graph_context", None)
                actual_results = getattr(results, "results", [])
            else:
                # Results is a list of result items (legacy format)
                actual_results = results if isinstance(results, list) else []

            # Process individual search results (without entities/relationships)
            for result in actual_results:
                result_model = search_pb2.SearchResultItem(
                    file_id=result["file_id"],
                    file_name=result["file_name"],
                    mime_type=result.get("mime_type", ""),
                    web_view_link=result.get("web_view_link", ""),
                    created_time=result["created_time"],
                    modified_time=result["modified_time"],
                    score=float(result["score"]),
                    vector_id=result["vector_id"],
                    chunk_text=result.get("chunk_text", ""),
                    search_type=result.get("search_type", "hybrid"),
                )
                result_models.append(result_model)

            # Create GraphContext proto from graph_context_data
            graph_context_proto = None
            if graph_context_data:
                # Convert all entities
                all_entities_proto = []
                for entity in graph_context_data.get("all_entities", []):
                    entity_proto = search_pb2.EntityInfo(
                        id=entity.get("node_id", ""),
                        name=entity.get("name", ""),
                        type=entity.get("type", ""),
                        properties=convert_properties_to_string_map(
                            entity.get("properties", {})
                        ),
                        relevance_score=float(entity.get("relevance_score", 0.0)),
                    )
                    all_entities_proto.append(entity_proto)

                # Convert all relationships
                all_relationships_proto = []
                for relationship in graph_context_data.get("all_relationships", []):
                    relationship_proto = search_pb2.RelationshipInfo(
                        id=relationship.get("id", ""),
                        type=relationship.get("type", ""),
                        source_entity_id=relationship.get("source_entity_id", ""),
                        target_entity_id=relationship.get("target_entity_id", ""),
                        source_entity_name=relationship.get("source", ""),
                        target_entity_name=relationship.get("target", ""),
                        properties=convert_properties_to_string_map(
                            relationship.get("properties", {})
                        ),
                        confidence_score=float(
                            relationship.get("confidence_score", 0.0)
                        ),
                        relevance_score=float(relationship.get("relevance_score", 0.0)),
                        context=relationship.get("context", ""),
                    )
                    all_relationships_proto.append(relationship_proto)

                # Create the simplified GraphContext proto
                graph_context_proto = search_pb2.GraphContext(
                    all_entities=all_entities_proto,
                    all_relationships=all_relationships_proto,
                )

            # Create response with separate graph context
            response = search_pb2.SearchSimilarDocumentsResponse(
                success=True, message=message, results=result_models
            )

            # Add graph context if available
            if graph_context_proto:
                response.graph_context.CopyFrom(graph_context_proto)

            return response

        except Exception as e:
            # print(e)
            logger.error("Error searching similar documents", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error searching similar documents: {str(e)}")
            return search_pb2.SearchSimilarDocumentsResponse(success=False)

    def batchSearchSimilarDocuments(self, request, context):
        """
        Batch search for documents semantically similar to multiple query texts.
        """
        logger.info(
            "Received request for batch search of similar documents",
            user_id=request.user_id,
            query_count=len(request.query_texts),
            agent_id=request.agent_id if hasattr(request, "agent_id") else None,
            org_id=(
                request.organisation_id if hasattr(request, "organisation_id") else None
            ),
        )

        try:
            # Extract file_ids from request if provided
            file_ids = None
            if hasattr(request, "file_ids") and request.file_ids:
                file_ids = list(request.file_ids)
                logger.info(f"Filtering batch search to {len(file_ids)} specific files")

            # Extract least_score from request if provided
            least_score = None
            if hasattr(request, "least_score") and request.least_score > 0:
                least_score = request.least_score
                logger.info(
                    f"Filtering batch results with minimum score: {least_score}"
                )

            success, message, all_results = (
                self.drive_service.batch_search_similar_documents(
                    request.user_id,
                    request.query_texts,
                    request.top_k if request.top_k > 0 else 5,
                    (
                        request.agent_id
                        if hasattr(request, "agent_id") and request.agent_id
                        else None
                    ),
                    (
                        request.organisation_id
                        if hasattr(request, "organisation_id")
                        and request.organisation_id
                        else None
                    ),
                    file_ids,
                    least_score,
                )
            )

            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return search_pb2.BatchSearchSimilarDocumentsResponse(success=False)

            # Convert to proto models
            query_results_list = []

            for i, (query_text, results) in enumerate(
                zip(request.query_texts, all_results)
            ):
                # Convert results for this query to proto models
                result_models = []
                for result in results:
                    # Convert entities to proto format
                    entity_protos = []
                    for entity in result.get("entities", []):
                        entity_proto = search_pb2.EntityInfo(
                            id=entity.get("id", ""),
                            name=entity.get("name", ""),
                            type=entity.get("type", ""),
                            properties=convert_properties_to_string_map(
                                entity.get("properties", {})
                            ),
                        )
                        entity_protos.append(entity_proto)

                    # Convert relationships to proto format
                    relationship_protos = []
                    for relationship in result.get("relationships", []):
                        relationship_proto = search_pb2.RelationshipInfo(
                            id=relationship.get("id", ""),
                            type=relationship.get("type", ""),
                            source_entity_id=relationship.get("source_entity_id", ""),
                            target_entity_id=relationship.get("target_entity_id", ""),
                            properties=convert_properties_to_string_map(
                                relationship.get("properties", {})
                            ),
                        )
                        relationship_protos.append(relationship_proto)

                    result_model = search_pb2.SearchResultItem(
                        file_id=result["file_id"],
                        file_name=result["file_name"],
                        mime_type=result.get("mime_type", ""),
                        web_view_link=result.get("web_view_link", ""),
                        created_time=result["created_time"],
                        modified_time=result["modified_time"],
                        score=float(result["score"]),
                        vector_id=result["vector_id"],
                        chunk_text=result.get(
                            "chunk_text", ""
                        ),  # Include the chunk text for LLM context
                        search_type=result.get("search_type", "hybrid"),
                        entities=entity_protos,
                        relationships=relationship_protos,
                    )
                    result_models.append(result_model)

                # Create QueryResults for this query
                query_results = search_pb2.QueryResults(
                    query_text=query_text, results=result_models
                )
                query_results_list.append(query_results)

            return search_pb2.BatchSearchSimilarDocumentsResponse(
                success=True, message=message, query_results=query_results_list
            )

        except Exception as e:
            logger.error("Error in batch search for similar documents", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(
                f"Error in batch search for similar documents: {str(e)}"
            )
            return search_pb2.BatchSearchSimilarDocumentsResponse(success=False)

    def sync_file_by_url(
        self, drive_url: str, agent_id: str, organisation_id: str, user_id: str = None
    ):
        """
        Sync multiple files by URLs - supports both Google Drive URLs and generic HTTP/HTTPS URLs.
        """
        logger.info(
            "Received request to sync files by URLs",
            drive_urls=drive_url,
            agent_id=agent_id,
            user_id=user_id,
            organisation_id=organisation_id,
        )

        try:
            # Validate that we have URLs to process
            if not drive_url:
                # context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                # context.set_details("No URLs provided")
                return connector_pb2.SyncFileByUrlResponse(
                    success=False,
                    message="No URLs provided",
                    synced_files=[],
                    total_files=0,
                    successful_syncs=0,
                    failed_syncs=0,
                )

            # Import URL validator to determine URL type
            from app.utils.file_processing.url_validator import URLValidator

            url_validator = URLValidator()

            synced_files = []
            successful_syncs = 0
            failed_syncs = 0

            # Process each URL
            for drive_url in drive_url:
                try:
                    # Validate and determine URL type
                    is_valid, url_type, error_msg = (
                        url_validator.validate_and_classify_url(drive_url)
                    )

                    if not is_valid:
                        # Add failed sync info
                        synced_files.append(
                            connector_pb2.SyncedFileInfo(
                                file_id="",
                                file_name="",
                                drive_url=drive_url,
                                sync_status="failed",
                                error_message=error_msg,
                            )
                        )
                        failed_syncs += 1
                        continue

                    if url_type == "gdrive_id":
                        # Handle Google Drive URL
                        file_id = self.drive_service.extract_file_id_from_url(drive_url)
                        if not file_id:
                            synced_files.append(
                                connector_pb2.SyncedFileInfo(
                                    file_id="",
                                    file_name="",
                                    drive_url=drive_url,
                                    sync_status="failed",
                                    error_message="Invalid Google Drive URL",
                                )
                            )
                            failed_syncs += 1
                            continue

                        # Sync the Google Drive file
                        success, message, file_data = (
                            self.drive_service.sync_file_by_id(
                                file_id, agent_id, user_id, organisation_id
                            )
                        )
                        logger.debug(
                            f"sync_file_by_id returned: success={success}, message={message}, file_data={file_data}"
                        )
                    else:
                        # Handle generic HTTP/HTTPS URL
                        import uuid

                        file_id = str(
                            uuid.uuid4()
                        )  # Generate unique ID for generic files

                        # Sync the generic file
                        success, message, file_data = (
                            self.drive_service.sync_file_by_id(
                                file_id,
                                agent_id,
                                user_id,
                                organisation_id,
                                url=drive_url,  # Pass URL for generic processing
                            )
                        )
                        logger.debug(
                            f"sync_file_by_id 2 returned: success={success}, message={message}, file_data={file_data}"
                        )
                    if success:
                        if (
                            not file_data
                            or "id" not in file_data
                            or "name" not in file_data
                        ):
                            logger.error(f"Invalid file_data: {file_data}")
                            continue

                        # Add successful sync info
                        synced_files.append(
                            connector_pb2.SyncedFileInfo(
                                file_id=file_data["id"],
                                file_name=file_data["name"],
                                drive_url=drive_url,
                                sync_status="completed",
                                error_message="",
                            )
                        )
                        successful_syncs += 1
                    else:
                        # Add failed sync info
                        synced_files.append(
                            connector_pb2.SyncedFileInfo(
                                file_id="",
                                file_name="",
                                drive_url=drive_url,
                                sync_status="failed",
                                error_message=message,
                            )
                        )
                        failed_syncs += 1

                except Exception as url_error:
                    logger.error(
                        f"Error processing URL {drive_url}", error=str(url_error)
                    )
                    synced_files.append(
                        connector_pb2.SyncedFileInfo(
                            file_id="",
                            file_name="",
                            drive_url=drive_url,
                            sync_status="failed",
                            error_message=f"Error: {str(url_error)}",
                        )
                    )
                    failed_syncs += 1

            # Determine overall success
            overall_success = successful_syncs > 0
            total_files = len(drive_url)

            if successful_syncs == total_files:
                overall_message = f"All {total_files} files synced successfully"
            elif successful_syncs > 0:
                overall_message = f"{successful_syncs} of {total_files} files synced successfully, {failed_syncs} failed"
            else:
                overall_message = f"All {total_files} files failed to sync"

            respo = connector_pb2.SyncFileByUrlResponse(
                success=overall_success,
                message=overall_message,
                synced_files=synced_files,
                total_files=total_files,
                successful_syncs=successful_syncs,
                failed_syncs=failed_syncs,
            )
            return respo

        except Exception as e:
            logger.error("Error syncing files by URLs", error=str(e))
            # context.set_code(grpc.StatusCode.INTERNAL)
            # context.set_details(f"Error syncing files: {str(e)}")
            return connector_pb2.SyncFileByUrlResponse(
                success=False,
                message=f"Error: {str(e)}",
                synced_files=[],
                total_files=len(drive_url) if drive_url else 0,
                successful_syncs=0,
                failed_syncs=len(drive_url) if drive_url else 0,
            )

    def getServiceAccountTopLevelFolders(self, request, context):
        """
        Get top-level folders from service account.
        """
        logger.info(
            "Received request to get top-level folders from service account",
            organisation_id=request.organisation_id,
        )

        try:
            # Get service account credentials
            service = self.drive_service.get_service_account_drive_service(
                request.organisation_id
            )
            if not service:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("No Google Drive service account credentials found")
                return connector_pb2.GetServiceAccountTopLevelFoldersResponse(
                    success=False,
                    message="No Google Drive service account credentials found",
                )

            # Get top-level folders using the service account manager
            success, message, folders = (
                self.drive_service.service_account_manager.validate_service_account_access(
                    self.drive_service.get_service_account_credentials_json(
                        request.organisation_id
                    )
                )
            )

            if not success:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(message)
                return connector_pb2.GetServiceAccountTopLevelFoldersResponse(
                    success=False, message=message
                )

            # Convert folders to proto models
            folder_models = []
            for folder in folders:
                folder_model = connector_pb2.FolderInfo(
                    id=folder["id"], name=folder["name"]
                )
                folder_models.append(folder_model)

            return connector_pb2.GetServiceAccountTopLevelFoldersResponse(
                success=True,
                message=f"Found {len(folder_models)} top-level folders",
                folders=folder_models,
            )

        except Exception as e:
            logger.error(
                "Error getting top-level folders from service account", error=str(e)
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error getting top-level folders: {str(e)}")
            return connector_pb2.GetServiceAccountTopLevelFoldersResponse(
                success=False, message=f"Error: {str(e)}"
            )
